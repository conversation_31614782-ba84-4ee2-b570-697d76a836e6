import requests

# Your Twilio credentials
account_sid = "**********************************"
auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7" # Replace with your actual Auth Token

# The recording URL
# recording_url = "https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE069d0276b7b56949ed4a362f381d6c89"

recording_urls = ['https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE4097bcd37b0eba333136f31210f0be04','https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE53f4f177a8c1ef9ceeaadc99ff55f5d2','https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE069d0276b7b56949ed4a362f381d6c89','https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE2f9ae652647ba2a52a1d1b6223378759','https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE450c942f4a8e6ab140e3c48722160df3','https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/REd3b237f35822344d26f71d07d93ce480']
# Make an authenticated GET request
for recording_url in recording_urls:
    response = requests.get(recording_url, auth=(account_sid, auth_token))

    # Check if the request was successful
    if response.status_code == 200:
        # Save the recording to a file
        with open("call_recording.wav", "wb") as f:
            f.write(response.content)
        print("Recording downloaded successfully as call_recording.wav")
    else:
        print(f"Failed to download recording. Status: {response.status_code}")
    print(f"Response: {response.text}")