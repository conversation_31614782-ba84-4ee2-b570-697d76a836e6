async def getDefaultTools():
    return [
        {
            "type": "function",
            "name": "call_later",
            "description": "CALL ONLY when customer explicitly requests callback."
            " Ask: '<PERSON>ya mei apko baad mein call karun? Kab convenient hoga?' "
            "Confirm the exact time with the customer. "
            "CALLBACK_TIME must be EXACTLY: DD-MM-YYYY HH:MM AM/PM (24-hour is not allowed). "
            "Examples: '05-09-2025 04:00 PM', '12-11-2025 09:30 AM'."
            " If the customer gives a relative phrase (e.g., 'aaj 4pm'), "
            "first confirm: 'Theek hay, mai aapko 05-09-2025 04:00 PM ko call karungi. Theek hay?' "
            "THEN call this function using the confirmed string only.",
            "parameters": {
                "type": "object",
                "properties": {
                    "callback_time": {
                        "type": "string",
                        "description": '''
                        Exact date/time string provided and confirmed by the customer, format: DD-MM-YYYY HH:MM AM/PM.
                        Must be customer's specified time, NOT your suggestion.
                        '''
                    }
                },
                "required": ["callback_time"]
            }
        },
        
        {
            "type": "function",
            "name": "mark_lead_as_successful",
            "description": '''

            EXTREMELY STRICT: call ONLY when ALL of the following TRUE:
              - Full script Steps A through E were completed in order.
              - Closing question was asked exactly: "kia aap is sahulat main shamil hona chahay ge?"
              - Customer gave an explicit affirmative confirmation (see 'customer_confirmation').
              - Customer explicitly confirmed they understand price: "yeh sahulat Rs. 2,950 salana ..." (assistant must have said this).
              - Customer's age confirmed to be between 18 and 65 inclusive.
            CALLING PROCESS:
              1) Assistant utters the exact final confirmation line (Step F) verbatim.
              2) Wait for audio/response.completed event (ensure speech finished).
              3) THEN invoke this function with validated fields below.
            If any required validation fails, DO NOT call; instead ask one clarifying question.

            ''',
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_confirmation": {
                        "type": "string",
                        "description": "Exact words (verbatim) customer used to confirm (e.g., 'Haan', 'Ji haan', 'Main shamil hona chahti hun')."
                    },
                    "closing_question_asked": {
                        "type": "boolean", 
                        "description": "true if the assistant asked the exact closing question before confirmation."
                    },
                    "annual_price_confirmed": {
                        "type": "boolean",
                        "description": "true if assistant stated the price and customer acknowledged it."
                    },
                    "age": {
                        "type": "integer",
                        "minimum": 18,
                        "maximum": 65,
                        "description": "Customer's confirmed age (integer). Per script, must be 18-65. ONLY the customer can provide this value. DO NOT ASSUME IT YOURSELF."
                    },
                    "timestamp_iso": {
                        "type": "string",
                        "format": "date-time",
                        "description": "Server timestamp when lead was marked successful."
                    }
                },
                "required": ["closing_question_asked", "annual_price_confirmed", "age"]
            }
        }
    ]