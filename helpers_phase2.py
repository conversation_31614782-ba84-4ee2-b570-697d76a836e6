import asyncio
import json
import logging
import aiohttp

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def handle_phase2_confirmation(openai_ws, booking_id):
    try:
        confirmation_message = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "system",
                "content": [
                    {
                        "type": "input_text",
                        "text": (
                            "The customer pressed 1 to confirm the subscription. "
                            "Tell them in Urdu: "
                            "'Aap kamyabi se Family Health Insurance ka salaana plan hasil kar chukay hain. "
                            "Aap ye plan 14 din tak aazma saktay hain. "
                            "Agar is doran aap plan cancel karna chahain tou JazzCash ki helpline 4444 pe call kar k ye plan cancel kar k refund le saktay hain. "
                            "Claim karnay k liye aap JazzCash App k insurance section me jain ya phir EFU ki helpline 042 111 333 033 pe call karain. "
                            "<PERSON><PERSON><PERSON> aur Khuda Ha<PERSON>z.' "
                            "Then end the call politely."
                        )
                    }
                ]
            }
        }
        await openai_ws.send(json.dumps(confirmation_message))
        await openai_ws.send(json.dumps({"type": "response.create"}))
        logger.info(f"SUBSCRIPTION CONFIRMED - Booking {booking_id}")
    except Exception as e:
        logger.error(f"Failed to send Phase 2 confirmation: {e}")

async def process_efu_subscription(to_number: str, EFU_API_BASE_URL: str, EFU_BEARER_TOKEN: str) -> None:
    """
    Process EFU family health insurance subscription via API call.

    Args:
        to_number: Customer phone number in international format (+92...)
    """
    async with aiohttp.ClientSession() as session:
        try:
            params = {
                "plan_id": 4,
                "product_id": 10,
                "subscriber_msisdn": to_number.replace('+92', '0')
            }
            headers = {
                "Authorization": f"Bearer {EFU_BEARER_TOKEN}",
                "Accept": "application/json",
                "User-Agent": "python-requests/2.31.0",
            }

            logger.info(f"API Link: {EFU_API_BASE_URL} params={params}")
            timeout = aiohttp.ClientTimeout(total=10.0)

            # Send POST with no body (like requests.post(url) did)
            async with session.post(EFU_API_BASE_URL, params=params, headers=headers, timeout=timeout) as response:
                resp_text = await response.text()
                if response.status >= 400:
                    logger.error(f"HTTPS request failed to {response.url}: {response.status} body={resp_text}")
                    response.raise_for_status()
                try:
                    result = await response.json(content_type=None)
                except Exception:
                    result = {"text": resp_text}
                logger.info(f"Status Code: {response.status}")
                logger.info(f"Response: {result}")
        except aiohttp.ClientError as exc:
            logger.error(f"HTTPS request failed to {EFU_API_BASE_URL}: {exc}")


async def handle_phase2_dtmf(conn_state, dtmf, booking_id, to_number, EFU_API_BASE_URL, EFU_BEARER_TOKEN):
    """
    Handle DTMF input during Phase 2 of the call.

    Args:
        conn_state: Connection state object containing openai_ws, wrong_dtmf_attempts, etc.
        dtmf: The DTMF digit pressed by the customer
        booking_id: The booking ID for the current call
        to_number: Customer phone number for EFU subscription
    """
    if dtmf == '1':
        logger.info(f'Phase 2 final confirmation received for booking_id: {booking_id}')
        conn_state.wrong_dtmf_attempts = 0

        tasks = [
            process_efu_subscription(to_number, EFU_API_BASE_URL, EFU_BEARER_TOKEN),
            handle_phase2_confirmation(conn_state.openai_ws, booking_id)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, Exception):
                logger.error(f"A task failed: {result}")

        logger.info(f'EFU Subscription processed for booking_id: {booking_id}')
    else:
        logger.info(f"Received DTMF '{dtmf}' in phase 2 but it's not '1'")

        conn_state.wrong_dtmf_attempts += 1
        logger.warning(f'Wrong DTMF pressed in Phase 2: {dtmf}, attempt #{conn_state.wrong_dtmf_attempts}')

        if conn_state.wrong_dtmf_attempts == 1:
            # First wrong attempt - polite correction
            correction_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "system",
                    "content": [
                        {
                            "type": "input_text",
                            "text": """The customer pressed the wrong button.
                            Say in Urdu: 'Bara-e-mehrbani plan hasil karne k liye 1 (aik) ka button dabain.'
                            Then wait for their response."""
                        }
                    ]
                }
            }
            await conn_state.openai_ws.send(json.dumps(correction_message))
            await conn_state.openai_ws.send(json.dumps({"type": "response.create"}))

        elif conn_state.wrong_dtmf_attempts == 2:
            # Second wrong attempt - more explicit
            correction_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "system",
                    "content": [
                        {
                            "type": "input_text",
                            "text": """The customer pressed the wrong button again.
                            Say in Urdu: 'Muaziz sarif, aapnay ghalat number dial kia hai.
                            Agar aap yeh plan lagana chahte hain tou mehrbani kar ke 1 (aik) ka button dabain.
                            Ya phir agar aap nahi chahte tou mujhe bata dein.'
                            Then wait for their response."""
                        }
                    ]
                }
            }
            await conn_state.openai_ws.send(json.dumps(correction_message))
            await conn_state.openai_ws.send(json.dumps({"type": "response.create"}))

        elif conn_state.wrong_dtmf_attempts >= 3:
            # Third or more wrong attempts - ask if they want to continue
            final_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "system",
                    "content": [
                        {
                            "type": "input_text",
                            "text": """The customer has pressed wrong buttons multiple times.
                            Say in Urdu: 'Apka Shukriya, Allah Hafiz.'
                            End the call."""
                        }
                    ]
                }
            }
            await conn_state.openai_ws.send(json.dumps(final_message))
            await conn_state.openai_ws.send(json.dumps({"type": "response.create"}))
            logger.info(f'Call ended due to multiple wrong DTMF attempts: {conn_state.wrong_dtmf_attempts}')
            await conn_state.openai_ws.close()

