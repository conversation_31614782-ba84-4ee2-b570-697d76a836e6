import os
import ujson as json
import base64
import asyncio
import argparse
import logging
import asyncpg
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, HTTPException,BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.rest import Client
import requests
import websockets
from dotenv import load_dotenv
import uvicorn
import re
from pydantic import BaseModel
from datetime import datetime
from dateutil.parser import parse
import math
import aiohttp
from typing import Optional

from tools_function import getDefaultTools
from transfer import play_hold_beeps, clear_audio_queue, wait_for_response_completion, clear_and_update_session, start_phase2_conversation
from helpers_phase2 import handle_phase2_confirmation, handle_phase2_dtmf, process_efu_subscription
from db import db_manager
from DBEngine import lifespan, get_pool, get_pool_ws
from prompts import get_sales_prompt

load_dotenv()
db = db_manager()

# Configuration
TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN')
PHONE_NUMBER_FROM = os.getenv('PHONE_NUMBER_FROM')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
MODEL = os.getenv('MODEL')
raw_domain = os.getenv('DOMAIN', '')
DOMAIN = re.sub(r'(^\w+:|^)\/\/|\/+$', '', raw_domain) # Strip protocols and trailing slashes from DOMAIN
DB_WRAPPER_API = os.getenv('DB_WRAPPPER_API')
PORT = int(os.getenv('PORT', 6060))
VOICE = os.getenv('VOICE')

# EFU API Configuration
EFU_API_BASE_URL = os.getenv('EFU_API_BASE_URL')
EFU_BEARER_TOKEN = os.getenv('EFU_BEARER_TOKEN')



LOG_EVENT_TYPES = [
    'error', 'response.content.done', 'rate_limits.updated', 'response.done',
    'input_audio_buffer.committed', 'input_audio_buffer.speech_stopped',
    'input_audio_buffer.speech_started', 'session.updated'
]
# LOG_EVENT_TYPES = [
#     'error', 'response.function_call_arguments.done'
# ]
SHOW_TIMING_MATH = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


# --- FastAPI Application Instance ---
app = FastAPI(lifespan=lifespan)

class CallRequest(BaseModel):
    from_number: str
    to_number: str
    mission: str
    organisation_id: str
    campaign_id: str
    booking_id: int
    customer_name: str
    call_id: Optional[str] = None  # Add this for tracking
    webhook_url: Optional[str] = None

if not (TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN and PHONE_NUMBER_FROM and OPENAI_API_KEY):
    raise ValueError('Missing Twilio and/or OpenAI environment variables. Please set them in the .env file.')

if not (EFU_API_BASE_URL and EFU_BEARER_TOKEN):
    raise ValueError('Missing EFU API configuration. Please set EFU_API_BASE_URL and EFU_BEARER_TOKEN in the .env file.')

# Initialize Twilio client
client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)


@app.get('/', response_class=JSONResponse)
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}


@app.websocket('/media-stream')
async def handle_media_stream(websocket: WebSocket, pool: asyncpg.Pool = Depends(get_pool_ws)):
    """Handle WebSocket connections between Twilio and OpenAI."""
    logger.info("Client connected")
    await websocket.accept()
    stream_sid = None
    latest_media_timestamp = 0
    last_assistant_item = None
    mark_queue = []
    response_start_timestamp_twilio = None
    dtmfs = None
    current_response_cumulative_duration_ms = 0

    # Create a shared state object to manage connection switching
    class ConnectionState:
        def __init__(self):
            self.openai_ws = None
            self.should_restart = False
            self.is_phase2 = False
            self.pending_transfer = False
            self.transfer_wait_time = 0  # Track time to wait before transfer
            self.dtmf_queue = []  # Queue DTMF digits during connection transitions
            self.wrong_dtmf_attempts = 0  # Track wrong button presses in Phase 2


    conn_state = ConnectionState()

    async for message in websocket.iter_text():
        data = json.loads(message)
        if data.get("event") == 'start':
            stream_sid = data['start']['streamSid']
            logger.info(f"Incoming stream has started {stream_sid}")
            mission = data.get("start", {}).get("customParameters", {}).get("mission", "unknown")
            organisation_id = data.get("start", {}).get("customParameters", {}).get("org_id", "unknown")
            campaign_id = data.get("start", {}).get("customParameters", {}).get("campaign_id", "unknown")
            booking_id = data.get("start", {}).get("customParameters", {}).get("booking_id", "unknown")
            customer_name = data.get("start", {}).get("customParameters", {}).get("customer_name", "unknown")
            to_number = data.get("start", {}).get("customParameters", {}).get("to_number", "unknown")

            call_identification = data.get("start", {}).get("customParameters", {}).get("call_id", "unknown")
            await db.insert_initial_call_record(pool, call_identification)
            logger.info(f'Initial call record inserted for call_id: {call_identification}')

            break


    logger.info(f'Organisation ID: {organisation_id}, Campaign ID: {campaign_id}, Call ID: {call_identification}')
    prompt, language = await db.read_campaign_info(pool, organisation_id, campaign_id)

    prompt = prompt.format(customer_name=customer_name)

    function_tools = await getDefaultTools()

    logger.info(f'Tools Length: {len(function_tools)}, Prompt length: {len(prompt)}, Customer Name: {customer_name}')

    # Initial connection
    conn_state.openai_ws = await websockets.connect(
        f'wss://api.openai.com/v1/realtime?model=gpt-realtime',
        extra_headers={
            "Authorization": f"Bearer {OPENAI_API_KEY}"
        }
    )
    logger.info("Initial OpenAI connection created")


    await initialize_session(conn_state.openai_ws, prompt, function_tools)

    async def receive_from_twilio():
        dtmf_list = []
        """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
        nonlocal stream_sid, latest_media_timestamp, dtmfs, conn_state, to_number

        try:
            async for message in websocket.iter_text():
                data = json.loads(message)

                # Check if we need to skip processing due to connection switch
                if conn_state.should_restart:
                    # Still capture DTMF during restart so it's not lost
                    if data.get('event') == 'dtmf':
                        digit_tmp = data['dtmf'].get('digit')
                        if digit_tmp:
                            conn_state.dtmf_queue.append(digit_tmp)
                            logger.info(f"Queued DTMF '{digit_tmp}' during restart")
                    continue

                # Check if we have a pending transfer
                if conn_state.pending_transfer and not conn_state.should_restart:
                    # Wait for the transfer announcement to complete
                    if conn_state.transfer_wait_time > 0:
                        await asyncio.sleep(conn_state.transfer_wait_time)
                        conn_state.transfer_wait_time = 0

                    conn_state.pending_transfer = False
                    conn_state.should_restart = True

                    # Transferring call, this changes to a new session with openai to simulate a real transfer experience
                    logger.info('Executing pending transfer after successful lead marking')
                    new_openai_ws = await call_transfer(
                        conn_state.openai_ws,
                        websocket,
                        stream_sid,
                        organisation_id,
                        campaign_id,
                        booking_id,
                        pool
                    )

                    # Update the connection state
                    conn_state.openai_ws = new_openai_ws
                    conn_state.is_phase2 = True
                    conn_state.should_restart = False

                    logger.info('Phase 2 activated with new connection')

                    # If any DTMF digits were queued during transfer, process them now
                    while conn_state.dtmf_queue:
                        digit = conn_state.dtmf_queue.pop(0)
                        logger.info(f"Processing queued DTMF after transfer: {digit}")
                        if digit == '1':
                            if conn_state.openai_ws and conn_state.openai_ws.open:
                                await handle_phase2_confirmation(conn_state.openai_ws, booking_id)
                            else:
                                logger.warning("OpenAI WS not open post-transfer; unable to process queued DTMF")
                    continue

                if data['event'] == 'media' and conn_state.openai_ws and conn_state.openai_ws.open:
                    latest_media_timestamp = int(data['media']['timestamp'])
                    audio_append = {
                        "type": "input_audio_buffer.append",
                        "audio": data['media']['payload']
                    }
                    await conn_state.openai_ws.send(json.dumps(audio_append))

                elif data['event'] == 'start':
                    stream_sid = data['start']['streamSid']
                    print(f"Incoming stream has started {stream_sid}")
                    response_start_timestamp_twilio = None
                    latest_media_timestamp = 0
                    last_assistant_item = None

                elif data['event'] == 'mark':
                    if mark_queue:
                        mark_queue.pop(0)

                elif data['event'] == 'dtmf':
                    logger.info(f"DTMF event received: {data}")
                    dtmf_payload = data.get('dtmf', {})
                    raw_digit = dtmf_payload.get('digit') or dtmf_payload.get('digits')
                    if raw_digit is None:
                        logger.warning(f"DTMF event missing digit(s): {dtmf_payload}")
                        continue
                    # Normalize to last pressed key (handles cases like '1#')
                    matches = re.findall(r"[0-9*#]", str(raw_digit))
                    dtmf = matches[-1] if matches else str(raw_digit)
                    logger.info(f"DTMF event received raw='{raw_digit}' normalized='{dtmf}'")

                    if organisation_id == 'jazzcash':
                        # If we're transferring or connection isn't ready, queue the digit
                        if conn_state.pending_transfer or conn_state.should_restart or not (conn_state.openai_ws and conn_state.openai_ws.open):
                            conn_state.dtmf_queue.append(dtmf)
                            logger.info(f"Queued DTMF '{dtmf}' during transfer/connection switch")
                        else:
                            # Only handle DTMF 1 in phase 2 for final confirmation
                            if conn_state.is_phase2:
                                await handle_phase2_dtmf(conn_state, dtmf, booking_id, to_number, EFU_API_BASE_URL, EFU_BEARER_TOKEN)
                            else:
                                # Not in phase 2 yet; queue it
                                conn_state.dtmf_queue.append(dtmf)
                                logger.info(f"Queued DTMF '{dtmf}' (not in phase 2 yet)")
                    else:
                        # Handle other organizations' DTMF
                        if dtmf == '#':
                            dtmfs = ''.join(dtmf_list)
                            print(f'DTMF dialed: {dtmfs}')
                            if conn_state.openai_ws and conn_state.openai_ws.open:
                                await send_dtmf_arguments(conn_state.openai_ws, dtmfs)
                            else:
                                logger.warning("OpenAI WS not open; cannot send DTMF arguments right now")
                            dtmf_list.clear()
                        else:
                            dtmf_list.append(dtmf)

        except WebSocketDisconnect:
            print("Client disconnected.")
            if conn_state.openai_ws and conn_state.openai_ws.open:
                await conn_state.openai_ws.close()

    async def send_to_twilio():
        """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
        nonlocal stream_sid, last_assistant_item, response_start_timestamp_twilio
        nonlocal dtmfs, current_response_cumulative_duration_ms, booking_id, conn_state, call_identification

        try:
            while True:
                # Check if connection exists and is open
                if not conn_state.openai_ws or not conn_state.openai_ws.open:
                    if conn_state.should_restart:
                        # Wait for new connection to be established
                        await asyncio.sleep(0.1)
                        continue
                    else:
                        # Connection closed unexpectedly
                        logger.error("OpenAI connection closed unexpectedly")
                        break

                try:
                    # Use wait_for to avoid hanging on a closed connection
                    openai_message = await asyncio.wait_for(
                        conn_state.openai_ws.recv(),
                        timeout=0.5
                    )
                except asyncio.TimeoutError:
                    # Check if we should restart
                    if conn_state.should_restart:
                        await asyncio.sleep(0.1)
                    continue
                except websockets.exceptions.ConnectionClosed:
                    if conn_state.should_restart:
                        # Expected during connection switch
                        logger.info("Connection closed for restart")
                        await asyncio.sleep(0.1)
                        continue
                    else:
                        logger.error("OpenAI WebSocket connection closed")
                        break

                response = json.loads(openai_message)

                if response['type'] in LOG_EVENT_TYPES:
                    logger.info(f"Received event: {response['type']}", response)

                if response.get('type') == 'response.output_audio.delta' and 'delta' in response:
                    websocket.response_in_progress = True

                    new_item_id = response.get('item_id')
                    if new_item_id and new_item_id != last_assistant_item:
                        current_response_cumulative_duration_ms = 0
                        last_assistant_item = new_item_id
                        response_start_timestamp_twilio = None

                    audio_payload = response['delta']
                    audio_delta = {
                        "event": "media",
                        "streamSid": stream_sid,
                        "media": {
                            "payload": audio_payload
                        }
                    }
                    await websocket.send_json(audio_delta)

                    decoded_audio = base64.b64decode(response['delta'])
                    bytes_per_millisecond = 8
                    chunk_duration_ms = len(decoded_audio) / bytes_per_millisecond
                    current_response_cumulative_duration_ms += chunk_duration_ms

                    if response_start_timestamp_twilio is None:
                        response_start_timestamp_twilio = latest_media_timestamp

                    await send_mark(websocket, stream_sid)

                if response.get('type') == 'input_audio_buffer.speech_started':
                    logger.info("Speech started detected.")
                    if last_assistant_item:
                        await handle_speech_started_event()

                if response.get('type') == 'response.done':
                    websocket.response_in_progress = False
                    print("Response done detected.")

                    for item in response['response']['output']:
                        if item['type'] == 'function_call':
                            arguments = json.loads(item['arguments'])
                            call_id = item["call_id"]
                            function_name = item['name']
                            result = None

                            logger.info(f"Function invoked: {function_name}, Arguments: {arguments}")

                            if function_name == 'call_later':
                                callback_time = arguments.get("callback_time")
                                if not callback_time:
                                    logger.error("No callback_time provided")
                                    result = {"status": "Error: Please provide a specific callback time"}
                                else:
                                    # Pass call_id instead of booking_id
                                    result = await get_call_later_message(callback_time, organisation_id, campaign_id, call_identification, pool)
                                    logger.info(f"Call rescheduled: {result}")

                                if result:
                                    function_output = json.dumps(result)
                                    response_message = {
                                        "type": "conversation.item.create",
                                        "item": {
                                            "type": "function_call_output",
                                            "output": function_output,
                                            "call_id": call_id
                                        }
                                    }
                                    await conn_state.openai_ws.send(json.dumps(response_message))
                                    await conn_state.openai_ws.send(json.dumps({"type": "response.create"}))

                            elif function_name == 'mark_lead_as_successful':
                                customer_confirmation = arguments.get("customer_confirmation", "")

                                if organisation_id == 'jazzcash' and not conn_state.is_phase2:
                                    # Phase 1 - validate and mark lead
                                    closing_question_asked = arguments.get("closing_question_asked", False)
                                    annual_price_confirmed = arguments.get("annual_price_confirmed", False)

                                    if not closing_question_asked:
                                        logger.warning(f"REJECTED LEAD: Closing question not asked - Call {call_identification}")
                                        result = {"status": "Please ask the closing question first before confirming"}
                                    elif not annual_price_confirmed:
                                        logger.warning(f"REJECTED LEAD: Price not confirmed - Call {call_identification}")
                                        result = {"status": "Please confirm the annual price with customer"}
                                    elif arguments.get("age") < 18 or arguments.get("age") > 65:
                                        logger.warning(f"REJECTED LEAD: Age not within 18-65 - Call {call_identification}")
                                        result = {"status": "Customer's age must be between 18 and 65"}
                                    else:
                                        # Valid lead - mark as successful using call_id
                                        result = await db.mark_lead_as_successful(pool, call_identification)  # Use call_id instead of booking_id

                                        # Set flag to trigger transfer after the bot mentions it
                                        conn_state.pending_transfer = True
                                        conn_state.transfer_wait_time = 6  # Wait 6 seconds for transfer announcement

                                        # Return success so the bot knows to announce the transfer
                                        result = {"status": "Lead marked successfully. "}
                                        logger.info(f'Lead has been officially marked. Initiating transfer in: {conn_state.transfer_wait_time}s')

                                    if result:
                                        function_output = json.dumps(result)
                                        response_message = {
                                            "type": "conversation.item.create",
                                            "item": {
                                                "type": "function_call_output",
                                                "output": function_output,
                                                "call_id": call_id
                                            }
                                        }
                                        await conn_state.openai_ws.send(json.dumps(response_message))
                                        logger.info(f"Lead marked as successful: {result}")
                                        # await conn_state.openai_ws.send(json.dumps({"type": "response.create"}))

        except Exception as e:
            print(f"Error in send_to_twilio: {e}")

    # Helper functions remain the same
    async def handle_speech_started_event():
        """Handle interruption when the caller's speech starts."""
        nonlocal response_start_timestamp_twilio, last_assistant_item
        nonlocal current_response_cumulative_duration_ms, stream_sid, mark_queue, websocket, conn_state

        if mark_queue and last_assistant_item:
            truncation_point_ms = math.floor(current_response_cumulative_duration_ms)
            truncation_point_ms = max(0, truncation_point_ms)

            print(f"Interrupting item {last_assistant_item} at {truncation_point_ms}ms")

            truncate_event = {
                "type": "conversation.item.truncate",
                "item_id": last_assistant_item,
                "content_index": 0,
                "audio_end_ms": truncation_point_ms
            }

            try:
                await conn_state.openai_ws.send(json.dumps(truncate_event))
                print(f"Sent truncate event for item {last_assistant_item}")
            except Exception as e:
                print(f"Error sending truncate event: {e}")

            await websocket.send_json({
                "event": "clear",
                "streamSid": stream_sid
            })

            mark_queue.clear()
            last_assistant_item = None
            response_start_timestamp_twilio = None
            current_response_cumulative_duration_ms = 0

    async def send_mark(connection, stream_sid):
        if stream_sid:
            mark_event = {
                "event": "mark",
                "streamSid": stream_sid,
                "mark": {"name": "responsePart"}
            }
            await connection.send_json(mark_event)
            mark_queue.append('responsePart')

    # Run both tasks
    await asyncio.gather(receive_from_twilio(), send_to_twilio())



async def send_dtmf_arguments(openai_ws, dtmfs):
    """Send Dial Pad Input to the model"""
    dtmf_input_json = {
        "type": "conversation.item.create",
        "item": {
            "id":"msg_001",
            "type": "message",
            "role": "system",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       f"The user entered: {dtmfs} via keypad. Call the relevant function and use these as function call arguments."
                    )
                }
            ]
        }
    }

    await openai_ws.send(json.dumps(dtmf_input_json))


async def call_transfer(openai_ws, websocket, stream_sid, organisation_id, campaign_id, booking_id, pool):
    """Handle call transfer to the Phase 2 Agent"""

    logger.info(f"Starting Phase 2 transfer for booking_id: {booking_id}")

    # Wait for announcement
    await asyncio.sleep(0.5)
    # So we can close the connection immediately

    # Step 1: Close the current OpenAI connection
    logger.info("Closing Phase 1 OpenAI connection")
    await openai_ws.close()

    # Step 2: Play hold beeps
    beep_task = asyncio.create_task(play_hold_beeps(websocket, stream_sid, duration=5))

    # Step 3: Create new OpenAI connection with different voice
    phase2_voice = "cedar"
    logger.info(f"Creating Phase 2 OpenAI connection with voice: {phase2_voice}")

    try:
        new_openai_ws = await websockets.connect(
            f'wss://api.openai.com/v1/realtime?model={MODEL}',
            extra_headers={
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }
        )
        logger.info("Phase 2 OpenAI connection created")

        # Phase 2 prompt - Senior agent for final confirmation
        phase2_prompt = """
        [Core Identity & Goal]

        You are a senior verification agent for EFU Family Health Insurance, calling on behalf of JazzCash. 
        Your one and only goal is to get the customer to press '1' on their keypad to confirm their subscription. 
        You are professional, direct, and your language is extremely clear and simple.

        [Context]

        The call is a transfer from another agent. 
        The customer has already been explained the plan details and has verbally agreed to purchase the EFU Family Health Insurance plan for a payment of 1950 Rupees from their JazzCash wallet. 
        Your job is not to sell, but to finalize the confirmation.

        [YOUR ROLE]
        - You are a DIFFERENT person (senior agent)
        - Speak only in simple, clear Urdu/Roman Urdu. Use short sentences. 
        - It is very important that you sound natural.

        [YOUR SCRIPT]
        1. Greet: "Thank you, aap ka taawun ka shukriya."
        2. Ask for confirmation: "Agar aapko Family Health Insurance Plan ki tafseelat samajh aa gayi hain aur aap apnay JazzCash wallet se 1950 rupay de kar is plan ko hasil karna chahtay hain tou 1 ka button dabain."
        3. Wait for them to press 1
        4. If they say no: "Koi baat nahi. Agar aap mustaqbil me yeh service chahain to JazzCash app ya helpline 4444 pe call kar saktay hain. Khuda Hafiz."

        [Rules of Engagement - CRITICAL]

        - THE ONLY TERMINATION CONDITION: You will ONLY end the call with the "Khuda Hafiz" script if the customer explicitly says a strong negative like:

        "Nahi chahiye" (I don't want it)

        "Cancel kar dein" (Cancel it)

        "Mujhe nahi lena" (I don't want to get it)

        "Baad mein karunga" (I will do it later)

        Pressing any dialpad number other than 1

        Any other clear and direct refusal.

        - HANDLE SILENCE & CONFUSION: If the customer is silent, says "Hello?", "Ji?", "Kya?", or anything unclear, you do NOT give up. You will repeat the core instruction. You must attempt to get a confirmation at least three times before ending the call.
        
        [IMPORTANT]
        - Do NOT re-explain the plan details, but answer any questions the customer asks you briefly from the FAQ knowledge base.
        - Immediately return to your primary goal. Do not get sidetracked. Your transition back should be: "Umeed hai is se aap ko wazeh ho gaya hoga. To chaliye, is plan ko activate karne ke liye..." (...I hope that's clear. So, to activate this plan...)
        - The system will handle the subscription confirmation when they press 1
        - Only answer questions based on the FAQ Knowledge Base. DO NOT make up answers.

        --- FREQUENTLY ASKED QUESTIONS (FAQ) KNOWLEDGE BASE ---
        Instructions for the AI: If a customer asks a question, check if it matches one of the topics below. Provide the corresponding answer concisely and then immediately try to return to the main goal of activating the customer's ssubscription.

        Topic: Number of Children Covered

        If the customer asks: "Agar meray do se zyada bachay hon tou unka kia ho ga?" (What if I have more than two children?)

        Your Answer: "Sir/Madam is plan me do bachon ko shamil kia ja sakta hai."

        Topic: How to Make a Claim

        If the customer asks: How to claim or receive money for expenses.

        Your Answer: "Claim karnay kay liye, aap Jazz Cash App ke Insurance section me ja kar claim kar saktay hain ya phir EFU ki helpline 042 111 333 033 pe call kar saktay hain. Claim k liye apna CNIC, hospital ki raseedain, aur doctor ka nuskha zaroor sambhaal kar rakhiyega."

        Topic: ICU Coverage Limit

        If the customer asks: "ICU me daakhlay ki soorat me kitni limit/coverage hoti hai?" (What is the ICU limit?)

        Your Answer: "ICU me daakhlay ki soorat me, har raat ka das hazaar rupay tak ka kharcha cover hota hai, aur pooray saal mein aap das laakh rupay tak ki raqam wasool kar saktay hain."

        Topic: Other Covered Expenses

        If the customer asks: "Aur konsay akhrajaat is plan me shaamil hain?" (What other expenses are included?)

        Your Answer: "Is plan mein haadsay ki soorat me Ambulance ke liye paintees sau rupay, aur accident ki soorat me haspataal k ikhrajaat k liye saath hazaar rupay tak ki raqam bhi shamil hai."

        Topic: Age Limit

        If the customer asks: "Kiya yeh 65 saal se ooper ke logon ke liye bhi hai?" (Is this for people above 65?)

        Your Answer: "Yeh offer atthara sey painsath (18-65) saal ki عمر ke logon kay liye hai."

        Topic: Family Members Included

        If the customer asks: "Kiya mere waldain yaa behan bhai included hein?" (Are my parents or siblings included?)

        Your Answer: "Is plan me mian, biwi aur 2 bachay covered hain."

        Topic: Questions You Don't Know the Answer To

        If the customer asks a question not covered here:

        Your Answer: "Iski mazeed tafseelaat aur sharait o zawabit ke liye, aap JazzCash App mein insurance section visit kar saktay hain ya phir Jazz Cash ki website dekh saktay hain."
        """


        await initialize_session(new_openai_ws, phase2_prompt, await getDefaultTools(), voice=phase2_voice)
        logger.info("Phase 2 session initialized")

        # Wait for beeps to finish
        await beep_task

        # Start phase 2 conversation
        logger.info("Starting Phase 2 conversation")
        await start_phase2_conversation(new_openai_ws)

        return new_openai_ws

    except Exception as e:
        logger.error(f"Failed to create Phase 2 connection: {e}")
        raise e

async def send_initial_conversation_item(openai_ws):
    """Send initial conversation so AI talks first."""
    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       "Greet the user with 'As-salamu alaykum! I am an AI voice assistant powered by Najoomie Technologies.'"
                        "Do not use 'Wa alaykum as-salam as a greeting."


                    )
                }
            ]
        }
    }
    print('introduction...')
    await openai_ws.send(json.dumps(initial_conversation_item))
    await openai_ws.send(json.dumps({"type": "response.create"}))

async def initialize_session(openai_ws, prompt, function_tools, voice=None):
    if voice is None:
        voice = "marin"

    session_update = {
        "type": "session.update",
        "session": {
            "type": "realtime",
            "model": "gpt-realtime-2025-08-25",
            "output_modalities": ["audio"],

            "audio": {
                "input": {
                    "format": {
                            "type": "audio/pcmu"
                            },
                    "noise_reduction": {
                        "type": "near_field"
                    },
                    "turn_detection": {
                        "type": "server_vad",
                        "threshold": 0.6,
                        "prefix_padding_ms": 300,
                        "silence_duration_ms": 500,
                        "create_response": True,
                        "interrupt_response": True,
                        "idle_timeout_ms": 6000
                    }
                },
                "output": {
                    "format": {
                            "type": "audio/pcmu"
                            },
                    "voice": voice,
                    "speed": 1.2
                }
            },

            "instructions": prompt,
            "tools": function_tools,
            "tool_choice": "auto"
        }
    }

    logger.info("Sending session update")
    await openai_ws.send(json.dumps(session_update))


# Add this endpoint to check call info
@app.get("/call_info/{call_id}")
async def get_call_info_endpoint(call_id: str, pool: asyncpg.Pool = Depends(get_pool)):
    """Get call information"""
    result = await db.get_call_info(pool, call_id)
    if result:
        return result
    else:
        raise HTTPException(status_code=404, detail="Call not found")

# Add this endpoint to see all successful leads
@app.get("/successful_leads")
async def get_successful_leads_endpoint(pool: asyncpg.Pool = Depends(get_pool)):
    """Get all successful leads"""
    return await db.get_successful_leads(pool)

async def check_number_allowed(to):
    """Check if a number is allowed to be called."""
    try:
        # Uncomment these lines to test numbers. Only add numbers you have permission to call
        # OVERRIDE_NUMBERS = ['+923034445823']
        # if to in OVERRIDE_NUMBERS:
        #   return True

        incoming_numbers = client.incoming_phone_numbers.list(phone_number=to)
        if incoming_numbers:
            return True

        outgoing_caller_ids = client.outgoing_caller_ids.list(phone_number=to)
        if outgoing_caller_ids:
            return True

        return False
    except Exception as e:
        #print(f"Error checking phone number: {e}")
        return False

@app.post("/make_call/")
async def make_call(call_request: CallRequest):
    """Make an outbound call with recording."""
    logger.info(f'Received call request: {call_request.json()}')
    
    from_number = call_request.from_number
    to_number = call_request.to_number
    mission = call_request.mission
    organisation_id = call_request.organisation_id
    campaign_id = call_request.campaign_id
    booking_id = call_request.booking_id
    customer_name = call_request.customer_name
    
    # Get optional fields
    call_id = call_request.call_id
    webhook_url = call_request.webhook_url
    
    if not from_number or not to_number:
        raise HTTPException(status_code=400, detail="Both 'from_number' and 'to_number' are required.")
    
    # TwiML with Stream for real-time processing
    outbound_twiml = (
        f'<?xml version="1.0" encoding="UTF-8"?>'
        f'<Response><Connect><Stream url="wss://{DOMAIN}/media-stream">'
        f'<Parameter name="mission" value="{mission}"/>'
        f'<Parameter name="org_id" value="{organisation_id}"/>'
        f'<Parameter name="campaign_id" value="{campaign_id}"/>'
        f'<Parameter name="from_number" value="{from_number}"/>'
        f'<Parameter name="to_number" value="{to_number}"/>'
        f'<Parameter name="booking_id" value="{booking_id}"/>'
        f'<Parameter name="customer_name" value="{customer_name}"/>'
        f'<Parameter name="call_id" value="{call_id}"/>'
        f'</Stream></Connect></Response>'
    )
    
    # Prepare call parameters with recording
    call_params = {
        "from_": from_number,
        "to": to_number,
        "twiml": outbound_twiml,
        "record": True,  # Enable recording
        "recording_channels": "dual",  # Keep channels separate
    }
    
    # Use the SAME webhook URL for both status and recording callbacks - use the SAME endpoint
    if webhook_url:
        # For status callbacks
        call_params["status_callback"] = webhook_url
        call_params["status_callback_event"] = ["completed", "busy", "no-answer", "failed", "canceled"]
        call_params["status_callback_method"] = "POST"
        
        # For recording callbacks - use the SAME endpoint
        call_params["recording_status_callback"] = webhook_url
        call_params["recording_status_callback_method"] = "POST"
        call_params["recording_status_callback_event"] = ["completed", "absent"]
    
    # Create the call with Twilio
    call = client.calls.create(**call_params)
    
    await log_call_sid(call.sid)
    
    response = {
        "message": f"Call successfully placed from {from_number} to {to_number}",
        "twilio_sid": call.sid,
        "recording_enabled": True
    }
    
    # Include call_id in response if provided
    if call_id:
        response["call_id"] = call_id
    
    return response

async def log_call_sid(call_sid):
    """Log the call SID."""
    print(f"Call started with SID: {call_sid}")

#...................................................................
async def get_call_later_message(callback_time, organisation_id, campaign_id, call_id, pool):  # Changed booking_id to call_id
    """This function will allow calls to be rescheduled, assuming no schedule conflicts"""

    callback_time = callback_time.lower()

    dt = format_demo_time(callback_time)

    if dt:
        # Use call_id instead of booking_id
        schedule_status = await db.reschedule_call(pool, call_id, schedule_time=str(dt))

        if schedule_status == 'rescheduled':
            return {"status": f"Your call has been rescheduled at: {dt}. Take care!"}
        elif schedule_status == 'already booked':
            return {"status": "This time is already booked. Please select a different time."}
        elif schedule_status == 'call_not_found':
            return {"status": "Call record not found. Please contact support."}
    else:
        return {"status": "An error occurred. Please try again later."}

def format_demo_time(demo_time):
    try:
        dt = parse(demo_time)
        dt = dt.replace(year=datetime.now().year)
        # print("Parsed datetime:", dt)
        return dt
    except Exception as e:
        print("Could not parse demo time:", e)
        return None


async def get_book_demo_message(demo_time,from_number, to_number, organisation_id, campaign_id):
    demo_time = demo_time.lower()

    dt = format_demo_time(demo_time)
    return {"status": "Your demo has successfully been booked. We look forward to providing"}

async def parse_parameters(call_params):
    if isinstance(call_params, str) and call_params != "unknown":
        try:
            # Parse the query string format: key='value' key='value'
            params_dict = {}
            # Split by spaces but handle quoted values
            import re
            # Pattern to match key='value' or key="value"
            pattern = r"(\w+)=['\"]([^'\"]*)['\"]"
            matches = re.findall(pattern, call_params)

            for key, value in matches:
                params_dict[key] = value

            call_params = params_dict
            logger.info(f"Parsed call_params: {call_params}")

        except Exception as e:
            logger.error(f"Failed to parse call_params: {e}")
            logger.info(f"Raw call_params value: '{call_params}'")
            # Fallback to default values if parsing fails
            call_params = {}

        return call_params


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=PORT)

# {
# "call_schedule": [
# 	{
# 		"organisation_id" : "nayatel",
# 		"campaign_id" : "schedule_test",
# 		"number_to" : "+923328502715",
# 		"number_from" : "+12314987814",
#         "misison":"Book a demo",
# 		"booking_id" : 34,
# 	}
# ]}