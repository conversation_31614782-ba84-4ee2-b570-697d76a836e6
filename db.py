import psycopg2
import os, json
from dotenv import load_dotenv
import select
import requests
import json
import asyncio
import websockets
import asyncpg
from fastapi import FastAPI, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream, Parameter, Say
import logging
from datetime import datetime
load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class db_manager:

    def __init__(self):
        
        self.db_params = {
        'dbname': os.getenv('dbname'),
        'user': os.getenv('user'),
        'password': os.getenv('password'),
        'host':os.getenv('host'),
        'port': os.getenv('port')
        }

    async def insert_initial_call_record(self, pool: asyncpg.Pool, call_id: str):
        """
        Insert initial call record into call_info table when call starts
        """
        try:
            logger.info(f'Inserting initial call record for call_id: {call_id}')
            async with pool.acquire() as conn:
                insert_query = """
                    INSERT INTO chatbot.call_info (call_id)
                    VALUES ($1)
                    ON CONFLICT (call_id) DO NOTHING;
                    """
                
                await conn.execute(insert_query, call_id)
                logger.info(f'Initial call record inserted for call_id: {call_id}')
                return True
                
        except Exception as e:
            logger.error(f"Database error inserting initial call record: {e}")
            return False

    async def read_campaign_info(self, pool: asyncpg.Pool, organisation_id: str, campaign_id: str):
        
        try:
            logger.info(f'Reading campaign info for Organisation ID: {organisation_id}, Campaign ID: {campaign_id}')
            async with pool.acquire() as conn:
                query = """
                    SELECT languages, service_detail
                    FROM chatbot.registered_campaign
                    WHERE organisation_id = $1 AND campaign_id = $2;
                    """

                results = await conn.fetchrow(query, organisation_id, campaign_id) 
                # logger.info(f'Data received: {results}')
                language = results['languages']
                service_detail = results['service_detail']

                # logger.info(f"Data received: Service Details: {service_detail}, language: {language}")
                return service_detail, language
           

        except Exception as e:
            print(f"Database error: {e}")
            return None
        
    async def reschedule_call(self, pool: asyncpg.Pool, call_id: str, schedule_time):
        """
        Update call_info table to mark reschedule_status = true and set schedule_time
        """
        try:
            # Convert schedule_time string to datetime object if it's a string
            if isinstance(schedule_time, str):
                try:
                    # Parse the datetime string - adjust format as needed
                    schedule_time = datetime.strptime(schedule_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    # Try alternative format if the first one fails
                    try:
                        schedule_time = datetime.strptime(schedule_time, '%Y-%m-%d %H:%M')
                    except ValueError:
                        logger.error(f"Invalid datetime format: {schedule_time}")
                        return None

            async with pool.acquire() as conn:
                # Check if this time slot is already taken by another call
                check_query = """
                    SELECT call_id
                    FROM chatbot.call_info
                    WHERE schedule_time = $1 AND call_id != $2;
                    """

                result = await conn.fetchrow(check_query, schedule_time, call_id) 
                if result:
                    logger.info(f'Time slot {schedule_time} already booked by call_id: {result["call_id"]}')
                    return 'already booked'
            
                else:
                    logger.info(f'Rescheduling call_id: {call_id} to {schedule_time}')
                
                    update_query = """
                        UPDATE chatbot.call_info
                        SET reschedule_status = true, schedule_time = $1
                        WHERE call_id = $2;
                        """
                    
                    result = await conn.execute(update_query, schedule_time, call_id)
                    
                    if result == "UPDATE 1":
                        logger.info(f"Call {call_id} successfully rescheduled to {schedule_time}")
                        return 'rescheduled'
                    else:
                        logger.warning(f"No record found for call_id: {call_id}")
                        return 'call_not_found'
           
        except Exception as e:
            logger.error(f"Database error in reschedule_call: {e}")
            return None
        
    async def mark_lead_as_successful(self, pool: asyncpg.Pool, call_id: str):
        """
        Update call_info table to mark mark_event = true for successful lead
        """
        try:
            async with pool.acquire() as conn:
                update_query = """
                    UPDATE chatbot.call_info
                    SET mark_event = true
                    WHERE call_id = $1;
                    """
                
                result = await conn.execute(update_query, call_id)
                
                if result == "UPDATE 1":
                    logger.info(f"Lead successfully updated in database for call_id: {call_id}")
                    return 'Lead marked as successful'
                else:
                    logger.warning(f"No record found for call_id: {call_id}")
                    return 'call_not_found'
            
        except Exception as e:
            logger.error(f"Database error in mark_lead_as_successful: {e}")
            return None

    async def get_call_info(self, pool: asyncpg.Pool, call_id: str):
        """
        Get call information from call_info table
        """
        try:
            async with pool.acquire() as conn:
                query = """
                    SELECT call_id, mark_event, reschedule_status, schedule_time
                    FROM chatbot.call_info
                    WHERE call_id = $1;
                    """
                
                result = await conn.fetchrow(query, call_id)
                
                if result:
                    return {
                        'call_id': result['call_id'],
                        'mark_event': result['mark_event'],
                        'reschedule_status': result['reschedule_status'],
                        'schedule_time': result['schedule_time']
                    }
                else:
                    logger.warning(f"No record found for call_id: {call_id}")
                    return None
                    
        except Exception as e:
            logger.error(f"Database error in get_call_info: {e}")
            return None

    async def get_scheduled_calls(self, pool: asyncpg.Pool, start_time=None, end_time=None):
        """
        Get all scheduled calls within a time range
        """
        try:
            async with pool.acquire() as conn:
                if start_time and end_time:
                    query = """
                        SELECT call_id, schedule_time, mark_event, reschedule_status
                        FROM chatbot.call_info
                        WHERE schedule_time BETWEEN $1 AND $2
                        ORDER BY schedule_time;
                        """
                    results = await conn.fetch(query, start_time, end_time)
                else:
                    query = """
                        SELECT call_id, schedule_time, mark_event, reschedule_status
                        FROM chatbot.call_info
                        WHERE schedule_time IS NOT NULL
                        ORDER BY schedule_time;
                        """
                    results = await conn.fetch(query)
                
                return [dict(row) for row in results]
                    
        except Exception as e:
            logger.error(f"Database error in get_scheduled_calls: {e}")
            return []

    async def get_successful_leads(self, pool: asyncpg.Pool):
        """
        Get all calls where mark_event = true (successful leads)
        """
        try:
            async with pool.acquire() as conn:
                query = """
                    SELECT call_id, schedule_time, reschedule_status
                    FROM chatbot.call_info
                    WHERE mark_event = true
                    ORDER BY call_id;
                    """
                
                results = await conn.fetch(query)
                return [dict(row) for row in results]
                    
        except Exception as e:
            logger.error(f"Database error in get_successful_leads: {e}")
            return []